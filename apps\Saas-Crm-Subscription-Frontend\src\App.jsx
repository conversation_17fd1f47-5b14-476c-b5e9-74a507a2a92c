import React from 'react';
import { Toaster } from 'react-hot-toast';
import SubscriptionRequest from './pages/SubscriptionRequest';



function App() {
    return (
        <div className="App">
            <SubscriptionRequest />
            <Toaster
                position="top-right"
                toastOptions={{
                    duration: 4000,
                    style: {
                        background: '#363636',
                        color: '#fff',
                    },
                    success: {
                        duration: 3000,
                        theme: {
                            primary: 'green',
                            secondary: 'black',
                        },
                    },
                    error: {
                        duration: 5000,
                        theme: {
                            primary: 'red',
                            secondary: 'black',
                        },
                    },
                }}
            />
        </div>
    );
}

export default App;
