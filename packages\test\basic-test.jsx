import React from 'react';
import { AdminLayout, <PERSON><PERSON>, <PERSON><PERSON>, User<PERSON>enu, ThemeToggle } from '../index.js';

// Basic test to ensure all components can be imported and used
const BasicTest = () => {
  const testUser = {
    name: 'Test User',
    email: '<EMAIL>',
    role: 'Admin'
  };

  const testNotifications = [
    {
      id: '1',
      title: 'Test Notification',
      message: 'This is a test notification',
      type: 'info',
      timestamp: new Date(),
      read: false
    }
  ];

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Admin Layout Package Test</h1>
      
      <div className="space-y-4">
        <div className="border p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">UserMenu Component</h2>
          <UserMenu user={testUser} />
        </div>
        
        <div className="border p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">ThemeToggle Component</h2>
          <ThemeToggle />
        </div>
        
        <div className="border p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Full AdminLayout</h2>
          <div className="h-96 border rounded overflow-hidden">
            <AdminLayout 
              user={testUser} 
              notifications={testNotifications}
              className="h-full"
            >
              <div className="p-4">
                <h3 className="text-xl font-semibold">Test Content</h3>
                <p>This is test content inside the AdminLayout component.</p>
              </div>
            </AdminLayout>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicTest;
