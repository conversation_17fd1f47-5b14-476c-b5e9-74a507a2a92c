import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '../lib/utils.js';

const Breadcrumb = ({ 
  className,
  separator = <ChevronRight className="h-4 w-4" />,
  homeIcon = <Home className="h-4 w-4" />,
  ...props 
}) => {
  const location = useLocation();
  const pathnames = location.pathname.split('/').filter((x) => x);

  // Generate breadcrumb items from pathname
  const breadcrumbItems = [
    {
      label: 'Home',
      href: '/admin',
      icon: homeIcon
    }
  ];

  let currentPath = '';
  pathnames.forEach((pathname, index) => {
    currentPath += `/${pathname}`;
    
    // Skip the first 'admin' segment as it's already covered by Home
    if (pathname === 'admin' && index === 0) return;
    
    // Format the pathname for display
    const label = pathname
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    breadcrumbItems.push({
      label,
      href: currentPath,
      isLast: index === pathnames.length - 1
    });
  });

  if (breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <nav 
      className={cn("flex items-center space-x-1 text-sm text-muted-foreground", className)}
      aria-label="Breadcrumb"
      {...props}
    >
      <ol className="flex items-center space-x-1">
        {breadcrumbItems.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <span className="mx-2 text-muted-foreground/50">
                {separator}
              </span>
            )}
            
            {item.isLast ? (
              <span className="font-medium text-foreground flex items-center gap-1">
                {item.icon}
                {item.label}
              </span>
            ) : (
              <Link
                to={item.href}
                className="hover:text-foreground transition-colors flex items-center gap-1"
              >
                {item.icon}
                {item.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumb;
