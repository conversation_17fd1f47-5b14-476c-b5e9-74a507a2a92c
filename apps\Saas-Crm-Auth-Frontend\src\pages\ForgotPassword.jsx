import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Mail, ArrowLeft, Send, CheckCircle, Sparkles } from 'lucide-react';

import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Alert, AlertDescription } from '../components/ui/alert';

import { useAuth } from '../contexts/AuthContext';

// Validation schema
const forgotPasswordSchema = z.object({
    email: z
        .string()
        .min(1, 'Email is required')
        .refine((email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email), {
            message: 'Please enter a valid email address',
        }),
});

const ForgotPassword = () => {
    const [isSuccess, setIsSuccess] = useState(false);
    const [submittedEmail, setSubmittedEmail] = useState('');

    const { forgotPassword, isLoading, error, isAuthenticated, clearError } = useAuth();
    const navigate = useNavigate();

    // Redirect if already authenticated
    useEffect(() => {
        if (isAuthenticated) {
            navigate('/dashboard', { replace: true });
        }
    }, [isAuthenticated, navigate]);

    // Clear errors when component mounts
    useEffect(() => {
        clearError();
    }, [clearError]);

    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting },
        setError,
    } = useForm({
        resolver: zodResolver(forgotPasswordSchema),
        defaultValues: {
            email: '',
        },
    });

    const onSubmit = async (data) => {
        try {
            const result = await forgotPassword(data.email);

            if (result.success) {
                setSubmittedEmail(data.email);
                setIsSuccess(true);
            } else {
                // Handle specific field errors
                if (result.error?.includes('email')) {
                    setError('email', { message: result.error });
                }
            }
        } catch (error) {
            console.error('Forgot password error:', error);
        }
    };

    const handleResendEmail = async () => {
        if (submittedEmail) {
            const result = await forgotPassword(submittedEmail);
            if (result.success) {
                // Show success message or update UI
            }
        }
    };

    const handleBackToLogin = () => {
        navigate('/login');
    };

    if (isSuccess) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-md w-full space-y-8">
                    <div className="text-center">

                        <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Accord
                        </h1>
                        <p className="mt-2 text-sm text-gray-600">
                            Welcome back! Please sign in to your account.
                        </p>
                    </div>


                    <Card className="mt-8">
                        <CardHeader className="space-y-1 text-center">
                            <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                                <CheckCircle className="w-6 h-6 text-green-600" />
                            </div>
                            <CardTitle className="text-2xl font-bold">Check Your Email</CardTitle>
                            <CardDescription>
                                We've sent password reset instructions to your email address.
                            </CardDescription>
                        </CardHeader>

                        <CardContent className="space-y-4">
                            <div className="text-center space-y-4">
                                <p className="text-sm text-gray-600">
                                    We sent a password reset link to:
                                </p>
                                <p className="font-medium text-gray-900">{submittedEmail}</p>
                                <p className="text-sm text-gray-600">
                                    Didn't receive the email? Check your spam folder or click the button below to resend.
                                </p>
                            </div>
                        </CardContent>

                        <CardFooter className="flex flex-col space-y-3">
                            <Button
                                onClick={handleResendEmail}
                                variant="outline"
                                className="w-full"
                                disabled={isLoading}
                            >
                                {isLoading ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                                        Resending...
                                    </>
                                ) : (
                                    <>
                                        <Send className="mr-2 h-4 w-4" />
                                        Resend Email
                                    </>
                                )}
                            </Button>

                            <Button
                                onClick={handleBackToLogin}
                                variant="ghost"
                                className="w-full"
                            >
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Sign In
                            </Button>
                        </CardFooter>
                    </Card>

                    {/* Help Text */}
                    <div className="text-center text-xs text-gray-500">
                        <p>
                            Still having trouble? Contact our{' '}
                            <Link to="/support" className="text-primary hover:text-primary/80">
                                support team
                            </Link>{' '}
                            for assistance.
                        </p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div className="text-center">

                    <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        Accord
                    </h1>
                    <p className="mt-2 text-sm text-gray-600">
                        Welcome back! Please sign in to your account.
                    </p>
                </div>


                <Card className="mt-8">
                    <CardHeader className="space-y-1">
                        <CardTitle className="text-2xl font-bold text-center">Forgot Password</CardTitle>
                        <CardDescription className="text-center">
                            Enter your email address and we'll send you a link to reset your password.
                        </CardDescription>
                    </CardHeader>

                    <form onSubmit={handleSubmit(onSubmit)}>
                        <CardContent className="space-y-4">
                            {/* Global Error Alert */}
                            {error && (
                                <Alert variant="destructive">
                                    <AlertDescription>{error}</AlertDescription>
                                </Alert>
                            )}

                            {/* Email Field */}
                            <div className="space-y-2">
                                <Label htmlFor="email">Email Address</Label>
                                <div className="relative">
                                    <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                    <Input
                                        id="email"
                                        type="email"
                                        placeholder="Enter your email address"
                                        className="pl-10"
                                        {...register('email')}
                                        disabled={isLoading || isSubmitting}
                                    />
                                </div>
                                {errors.email && (
                                    <p className="text-sm text-red-600">{errors.email.message}</p>
                                )}
                            </div>

                            {/* Instructions */}
                            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                                <div className="flex">
                                    <div className="ml-3">
                                        <h3 className="text-sm font-medium text-blue-800">
                                            What happens next?
                                        </h3>
                                        <div className="mt-2 text-sm text-blue-700">
                                            <ul className="list-disc list-inside space-y-1">
                                                <li>We'll send a secure link to your email</li>
                                                <li>Click the link to create a new password</li>
                                                <li>The link expires in 24 hours for security</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </CardContent>

                        <CardFooter className="flex flex-col space-y-4">
                            <Button
                                type="submit"
                                className="w-full"
                                disabled={isLoading || isSubmitting}
                            >
                                {isLoading || isSubmitting ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                        Sending Reset Link...
                                    </>
                                ) : (
                                    <>
                                        <Send className="mr-2 h-4 w-4" />
                                        Send Reset Link
                                    </>
                                )}
                            </Button>

                            {/* Back to Login */}
                            <Button
                                type="button"
                                variant="ghost"
                                className="w-full"
                                onClick={handleBackToLogin}
                                disabled={isLoading || isSubmitting}
                            >
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Sign In
                            </Button>
                        </CardFooter>
                    </form>
                </Card>

                {/* Footer */}
                <div className="text-center text-xs text-gray-500">
                    <p>
                        Remember your password?{' '}
                        <Link to="/login" className="text-primary hover:text-primary/80">
                            Sign in here
                        </Link>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default ForgotPassword;
