import React, { useState } from 'react';
import { <PERSON>rows<PERSON><PERSON>out<PERSON> as Router } from 'react-router-dom';
import { AdminLayout } from '../index.js';
import '../styles/admin-layout.css';

// Example usage of the AdminLayout component
const AdminLayoutExample = () => {
  const [notifications, setNotifications] = useState([
    {
      id: '1',
      title: 'New user registered',
      message: '<PERSON> has joined your platform',
      type: 'info',
      timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
      read: false
    },
    {
      id: '2',
      title: 'Payment received',
      message: 'Payment of $99.99 has been processed successfully',
      type: 'success',
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      read: false
    },
    {
      id: '3',
      title: 'Server maintenance',
      message: 'Scheduled maintenance will begin at 2:00 AM',
      type: 'warning',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      read: true
    },
    {
      id: '4',
      title: 'Error in payment processing',
      message: 'There was an issue processing payment for order #12345',
      type: 'error',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
      read: true
    }
  ]);

  const user = {
    name: 'Admin User',
    email: '<EMAIL>',
    avatar: null, // Will show initials
    role: 'Super Administrator'
  };

  const handleNotificationClick = (notification) => {
    console.log('Notification clicked:', notification);
    // Handle notification click (e.g., navigate to relevant page)
  };

  const handleMarkAsRead = (notificationId) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, read: true } : n
      )
    );
  };

  const handleMarkAllAsRead = () => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, read: true }))
    );
  };

  const handleLogout = () => {
    console.log('Logout clicked');
    // Handle logout logic
    alert('Logout functionality would be implemented here');
  };

  const handleSidebarToggle = (collapsed) => {
    console.log('Sidebar toggled:', collapsed);
  };

  return (
    <Router>
      <AdminLayout
        user={user}
        notifications={notifications}
        onSidebarToggle={handleSidebarToggle}
        className="min-h-screen"
      >
        {/* Example Dashboard Content */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
            <p className="text-muted-foreground">Welcome to your admin dashboard</p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-card p-6 rounded-lg border">
              <h3 className="text-sm font-medium text-muted-foreground">Total Users</h3>
              <p className="text-2xl font-bold text-foreground">1,234</p>
              <p className="text-xs text-green-600">+12% from last month</p>
            </div>
            
            <div className="bg-card p-6 rounded-lg border">
              <h3 className="text-sm font-medium text-muted-foreground">Revenue</h3>
              <p className="text-2xl font-bold text-foreground">$12,345</p>
              <p className="text-xs text-green-600">+8% from last month</p>
            </div>
            
            <div className="bg-card p-6 rounded-lg border">
              <h3 className="text-sm font-medium text-muted-foreground">Orders</h3>
              <p className="text-2xl font-bold text-foreground">567</p>
              <p className="text-xs text-red-600">-3% from last month</p>
            </div>
            
            <div className="bg-card p-6 rounded-lg border">
              <h3 className="text-sm font-medium text-muted-foreground">Active Sessions</h3>
              <p className="text-2xl font-bold text-foreground">89</p>
              <p className="text-xs text-green-600">+15% from last hour</p>
            </div>
          </div>

          {/* Content Sections */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-card p-6 rounded-lg border">
              <h2 className="text-xl font-semibold text-foreground mb-4">Recent Activity</h2>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-foreground">New user registration</span>
                  <span className="text-xs text-muted-foreground ml-auto">2 min ago</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-foreground">Payment processed</span>
                  <span className="text-xs text-muted-foreground ml-auto">5 min ago</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm text-foreground">System update completed</span>
                  <span className="text-xs text-muted-foreground ml-auto">1 hour ago</span>
                </div>
              </div>
            </div>

            <div className="bg-card p-6 rounded-lg border">
              <h2 className="text-xl font-semibold text-foreground mb-4">Quick Actions</h2>
              <div className="grid grid-cols-2 gap-3">
                <button className="p-3 bg-primary text-primary-foreground rounded-lg text-sm font-medium hover:bg-primary/90 transition-colors">
                  Add User
                </button>
                <button className="p-3 bg-secondary text-secondary-foreground rounded-lg text-sm font-medium hover:bg-secondary/80 transition-colors">
                  Generate Report
                </button>
                <button className="p-3 bg-accent text-accent-foreground rounded-lg text-sm font-medium hover:bg-accent/80 transition-colors">
                  Send Newsletter
                </button>
                <button className="p-3 bg-muted text-muted-foreground rounded-lg text-sm font-medium hover:bg-muted/80 transition-colors">
                  View Analytics
                </button>
              </div>
            </div>
          </div>
        </div>
      </AdminLayout>
    </Router>
  );
};

export default AdminLayoutExample;
