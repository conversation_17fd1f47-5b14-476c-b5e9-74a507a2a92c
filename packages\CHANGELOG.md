# Changelog

All notable changes to the Admin Layout package will be documented in this file.

## [1.0.0] - 2025-01-04

### Added
- Initial release of Admin Layout package
- Complete admin dashboard layout with React 19 and shadcn/ui
- Responsive sidebar navigation with collapsible functionality
- Header component with search, theme toggle, notifications, and user menu
- Breadcrumb navigation with automatic route detection
- User menu with profile, settings, and logout options
- Theme toggle with light/dark/system modes
- Notification bell with dropdown menu and badge count
- Comprehensive UI component library based on Radix UI
- CSS variables for consistent theming
- TypeScript-ready components (JSX with proper prop types)
- Accessibility features using Radix UI primitives
- Mobile-responsive design
- Print-friendly styles
- Example components and usage documentation

### Components Included
- `AdminLayout` - Main layout wrapper
- `Sidebar` - Collapsible navigation sidebar
- `Header` - Top header with actions
- `Breadcrumb` - Automatic breadcrumb navigation
- `UserMenu` - User dropdown menu
- `ThemeToggle` - Theme switching component
- `NotificationBell` - Notifications dropdown
- UI Components:
  - `Avatar` - User avatar component
  - `Button` - Styled button component
  - `Input` - Form input component
  - `ScrollArea` - Custom scrollable area
  - `Separator` - Visual separator
  - `Tooltip` - Hover tooltips
  - `DropdownMenu` - Dropdown menu system

### Dependencies
- React 19.1.0
- React DOM 19.1.0
- React Router DOM 7.7.0
- Radix UI components (latest versions)
- Lucide React icons 0.525.0
- Class Variance Authority 0.7.1
- clsx 2.1.1
- tailwind-merge 3.3.1
- React Resizable Panels 3.0.3
- cmdk 1.1.1

### Features
- ✅ Fully responsive design
- ✅ Dark/light theme support
- ✅ Accessibility compliant
- ✅ TypeScript ready
- ✅ Modern React 19 features
- ✅ Shadcn/ui component system
- ✅ Customizable navigation
- ✅ Notification system
- ✅ User management UI
- ✅ Search functionality
- ✅ Mobile-friendly
- ✅ Print styles included
