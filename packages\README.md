# Admin Layout Package

A comprehensive admin layout package built with React 19 and shadcn/ui components. This package provides a complete admin dashboard layout with sidebar navigation, header, breadcrumbs, user menu, theme toggle, and notifications.

## Features

- 🎨 **Modern Design** - Built with shadcn/ui components and Tailwind CSS
- 📱 **Responsive** - Works perfectly on desktop, tablet, and mobile devices
- 🌙 **Dark Mode** - Built-in theme toggle with system preference detection
- 🧭 **Navigation** - Collapsible sidebar with tooltips and breadcrumb navigation
- 🔔 **Notifications** - Notification bell with dropdown menu
- 👤 **User Menu** - Complete user menu with profile, settings, and logout
- ⚡ **Performance** - Optimized with React 19 and modern best practices
- ♿ **Accessible** - Built with accessibility in mind using Radix UI primitives

## Installation

Since this is a local package, you can import it directly in your React applications within the same workspace.

```bash
# Install dependencies in the packages folder
cd packages
npm install
```

## Usage

### Basic Usage

```jsx
import React from 'react';
import { AdminLayout } from 'admin-layout';

// Import the CSS styles
import 'admin-layout/styles/admin-layout.css';

function App() {
  const user = {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatar.jpg',
    role: 'Administrator'
  };

  const notifications = [
    {
      id: '1',
      title: 'New user registered',
      message: 'A new user has joined your platform',
      type: 'info',
      timestamp: new Date(),
      read: false
    }
  ];

  return (
    <AdminLayout 
      user={user}
      notifications={notifications}
    >
      <h1>Dashboard Content</h1>
      <p>Your admin content goes here...</p>
    </AdminLayout>
  );
}

export default App;
```

### Custom Navigation

```jsx
import React from 'react';
import { AdminLayout } from 'admin-layout';
import { Home, Users, Settings } from 'lucide-react';

const customNavigation = [
  {
    title: 'Dashboard',
    href: '/admin',
    icon: Home,
    badge: null
  },
  {
    title: 'Users',
    href: '/admin/users',
    icon: Users,
    badge: '12'
  },
  {
    title: 'Settings',
    href: '/admin/settings',
    icon: Settings,
    badge: null
  }
];

function App() {
  return (
    <AdminLayout navigation={customNavigation}>
      {/* Your content */}
    </AdminLayout>
  );
}
```

### Individual Components

You can also use individual components separately:

```jsx
import { 
  Sidebar, 
  Header, 
  UserMenu, 
  ThemeToggle, 
  NotificationBell,
  Breadcrumb 
} from 'admin-layout';

// Use components individually
function CustomLayout() {
  return (
    <div className="flex">
      <Sidebar navigation={navigation} />
      <div className="flex-1">
        <Header user={user} notifications={notifications} />
        <main>
          <Breadcrumb />
          {/* Content */}
        </main>
      </div>
    </div>
  );
}
```

## Components

### AdminLayout

The main layout component that combines all other components.

**Props:**
- `children` - React nodes to render in the main content area
- `className` - Additional CSS classes
- `sidebarCollapsed` - Initial sidebar collapsed state (default: false)
- `onSidebarToggle` - Callback when sidebar is toggled
- `user` - User object for the user menu
- `navigation` - Array of navigation items
- `notifications` - Array of notification objects

### Sidebar

Collapsible sidebar with navigation items.

**Props:**
- `collapsed` - Whether the sidebar is collapsed
- `onToggle` - Callback when toggle button is clicked
- `navigation` - Array of navigation items
- `className` - Additional CSS classes

### Header

Top header with search, theme toggle, notifications, and user menu.

**Props:**
- `user` - User object
- `notifications` - Array of notifications
- `onSidebarToggle` - Callback for mobile sidebar toggle
- `sidebarCollapsed` - Current sidebar state
- `showSearch` - Whether to show search input (default: true)
- `showBreadcrumb` - Whether to show breadcrumb (default: true)

### UserMenu

Dropdown menu for user actions.

**Props:**
- `user` - User object with name, email, avatar, role
- `onLogout` - Callback when logout is clicked
- `className` - Additional CSS classes

### ThemeToggle

Theme toggle button with light/dark/system options.

### NotificationBell

Notification bell with dropdown menu.

**Props:**
- `notifications` - Array of notification objects
- `onNotificationClick` - Callback when notification is clicked
- `onMarkAsRead` - Callback to mark notification as read
- `onMarkAllAsRead` - Callback to mark all notifications as read

### Breadcrumb

Automatic breadcrumb navigation based on current route.

## Data Structures

### User Object
```javascript
{
  name: 'John Doe',
  email: '<EMAIL>',
  avatar: '/avatar.jpg', // Optional
  role: 'Administrator'  // Optional
}
```

### Navigation Item
```javascript
{
  title: 'Dashboard',
  href: '/admin',
  icon: Home, // Lucide React icon component
  badge: '5'  // Optional badge text
}
```

### Notification Object
```javascript
{
  id: '1',
  title: 'Notification Title',
  message: 'Notification message', // Optional
  type: 'info', // 'info', 'success', 'warning', 'error'
  timestamp: new Date(),
  read: false
}
```

## Styling

The package includes a CSS file with all necessary styles and CSS variables for theming. Import it in your main application:

```css
@import 'admin-layout/styles/admin-layout.css';
```

### CSS Variables

The package uses CSS variables for consistent theming:

```css
:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.147 0.004 49.25);
  --primary: oklch(0.216 0.006 56.043);
  /* ... more variables */
}
```

## Dependencies

- React 19.1.0+
- React DOM 19.1.0+
- React Router DOM 7.7.0+
- Radix UI components
- Lucide React icons
- Tailwind CSS utilities
- Class Variance Authority
- clsx & tailwind-merge

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## License

MIT License
