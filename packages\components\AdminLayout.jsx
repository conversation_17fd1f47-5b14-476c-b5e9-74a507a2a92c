import React, { useState } from 'react';
import { cn } from '../lib/utils.js';
import Sidebar from './Sidebar.jsx';
import Header from './Header.jsx';

const AdminLayout = ({ 
  children, 
  className,
  sidebarCollapsed = false,
  onSidebarToggle,
  user,
  navigation = [],
  notifications = [],
  ...props 
}) => {
  const [collapsed, setCollapsed] = useState(sidebarCollapsed);

  const handleSidebarToggle = () => {
    const newCollapsed = !collapsed;
    setCollapsed(newCollapsed);
    if (onSidebarToggle) {
      onSidebarToggle(newCollapsed);
    }
  };

  return (
    <div className={cn("min-h-screen bg-background", className)} {...props}>
      {/* Sidebar */}
      <Sidebar 
        collapsed={collapsed}
        onToggle={handleSidebarToggle}
        navigation={navigation}
        className="fixed left-0 top-0 z-40"
      />
      
      {/* Main Content Area */}
      <div 
        className={cn(
          "transition-all duration-300 ease-in-out",
          collapsed ? "ml-16" : "ml-64"
        )}
      >
        {/* Header */}
        <Header 
          user={user}
          notifications={notifications}
          onSidebarToggle={handleSidebarToggle}
          sidebarCollapsed={collapsed}
          className="sticky top-0 z-30"
        />
        
        {/* Page Content */}
        <main className="flex-1 p-6">
          <div className="mx-auto max-w-7xl">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
