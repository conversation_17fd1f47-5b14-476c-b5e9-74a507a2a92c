{"name": "admin-layout", "version": "1.0.0", "main": "index.js", "license": "MIT", "private": true, "type": "module", "scripts": {"test": "echo 'Admin Layout Package - Tests would go here'", "build": "echo 'Admin Layout Package - Build complete'", "dev": "echo 'Admin Layout Package - Development mode'"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-resizable-panels": "^3.0.3", "react-router-dom": "^7.7.0", "tailwind-merge": "^3.3.1"}, "peerDependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}}