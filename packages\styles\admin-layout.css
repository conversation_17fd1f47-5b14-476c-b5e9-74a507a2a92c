/* Admin Layout Global Styles */
@import "tailwindcss";

/* CSS Variables for consistent theming */
:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.147 0.004 49.25);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.147 0.004 49.25);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.147 0.004 49.25);
  --primary: oklch(0.216 0.006 56.043);
  --primary-foreground: oklch(0.985 0.001 106.423);
  --secondary: oklch(0.97 0.001 106.424);
  --secondary-foreground: oklch(0.216 0.006 56.043);
  --muted: oklch(0.97 0.001 106.424);
  --muted-foreground: oklch(0.553 0.013 58.071);
  --accent: oklch(0.97 0.001 106.424);
  --accent-foreground: oklch(0.216 0.006 56.043);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.985 0.001 106.423);
  --border: oklch(0.923 0.003 48.717);
  --input: oklch(0.923 0.003 48.717);
  --ring: oklch(0.709 0.01 56.259);
  --chart-1: oklch(0.577 0.245 27.325);
  --chart-2: oklch(0.709 0.01 56.259);
  --chart-3: oklch(0.553 0.013 58.071);
  --chart-4: oklch(0.216 0.006 56.043);
  --chart-5: oklch(0.97 0.001 106.424);
}

.dark {
  --background: oklch(0.147 0.004 49.25);
  --foreground: oklch(0.985 0.001 106.423);
  --card: oklch(0.147 0.004 49.25);
  --card-foreground: oklch(0.985 0.001 106.423);
  --popover: oklch(0.147 0.004 49.25);
  --popover-foreground: oklch(0.985 0.001 106.423);
  --primary: oklch(0.985 0.001 106.423);
  --primary-foreground: oklch(0.216 0.006 56.043);
  --secondary: oklch(0.216 0.006 56.043);
  --secondary-foreground: oklch(0.985 0.001 106.423);
  --muted: oklch(0.216 0.006 56.043);
  --muted-foreground: oklch(0.553 0.013 58.071);
  --accent: oklch(0.216 0.006 56.043);
  --accent-foreground: oklch(0.985 0.001 106.423);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.985 0.001 106.423);
  --border: oklch(0.216 0.006 56.043);
  --input: oklch(0.216 0.006 56.043);
  --ring: oklch(0.553 0.013 58.071);
  --chart-1: oklch(0.577 0.245 27.325);
  --chart-2: oklch(0.709 0.01 56.259);
  --chart-3: oklch(0.553 0.013 58.071);
  --chart-4: oklch(0.216 0.006 56.043);
  --chart-5: oklch(0.97 0.001 106.424);
}

/* Base styles */
* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Admin Layout specific styles */
.admin-layout {
  min-height: 100vh;
  background-color: hsl(var(--background));
}

.admin-sidebar {
  background-color: hsl(var(--card));
  border-color: hsl(var(--border));
}

.admin-header {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
  backdrop-filter: blur(8px);
}

.admin-content {
  background-color: hsl(var(--background));
}

/* Scrollbar styles */
.admin-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.admin-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

.admin-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
}

.admin-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Animation utilities */
@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOutToLeft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

.slide-in-left {
  animation: slideInFromLeft 0.3s ease-out;
}

.slide-out-left {
  animation: slideOutToLeft 0.3s ease-out;
}

/* Focus styles for accessibility */
.admin-focus:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .admin-sidebar {
    position: fixed;
    z-index: 50;
    height: 100vh;
  }
  
  .admin-content {
    margin-left: 0;
  }
}

/* Print styles */
@media print {
  .admin-sidebar,
  .admin-header {
    display: none;
  }
  
  .admin-content {
    margin-left: 0;
    padding: 0;
  }
}
