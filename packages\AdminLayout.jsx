import React from 'react';
import { Menu } from 'lucide-react';
import { Button } from './ui/components/ui/button'; // Adjust path if needed

export default function AdminLayout({ children }) {
  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <aside className="w-64 bg-gray-900 text-white p-4 hidden md:block">
        <h2 className="text-xl font-bold mb-6">Admin Panel</h2>
        <nav className="space-y-2">
          <a href="#" className="block px-2 py-1 rounded hover:bg-gray-700">Dashboard</a>
          <a href="#" className="block px-2 py-1 rounded hover:bg-gray-700">Users</a>
          <a href="#" className="block px-2 py-1 rounded hover:bg-gray-700">Settings</a>
        </nav>
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Navbar */}
        <header className="flex items-center justify-between p-4 border-b">
          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button variant="outline" size="sm">
              <Menu className="h-5 w-5" />
            </Button>
          </div>
          <h1 className="text-lg font-semibold">Admin Dashboard</h1>
        </header>

        {/* Dynamic page content */}
        <main className="p-6 overflow-y-auto">
          {children ? children : 'Welcome to Admin Panel'}
        </main>
      </div>
    </div>
  );
}
