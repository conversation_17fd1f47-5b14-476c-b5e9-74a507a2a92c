import React, { useState } from 'react';
import {
    Mail,
    Phone,
    MapPin,
    Clock,
    Send,
    MessageSquare,
    Users,
    Headphones
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import toast from 'react-hot-toast';

const ContactSection = () => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        company: '',
        subject: '',
        message: '',
        inquiry_type: ''
    });
    const [isSubmitting, setIsSubmitting] = useState(false);

    const contactMethods = [
        {
            icon: Mail,
            title: 'Email Us',
            description: 'Send us an email and we\'ll respond within 24 hours',
            contact: '<EMAIL>',
            action: 'Send Email'
        },
        {
            icon: Phone,
            title: 'Call Us',
            description: 'Speak directly with our support team',
            contact: '+****************',
            action: 'Call Now'
        }
    ];

    const officeInfo = [
        {
            icon: MapPin,
            title: 'San Francisco Office',
            details: ['123 Market Street', 'San Francisco, CA 94105', 'United States']
        },
        {
            icon: Clock,
            title: 'Business Hours',
            details: ['Monday - Friday: 9:00 AM - 6:00 PM PST', 'Saturday: 10:00 AM - 4:00 PM PST', 'Sunday: Closed']
        },
        {
            icon: Users,
            title: 'Support Team',
            details: ['24/7 Technical Support', 'Dedicated Account Managers', 'Expert Implementation Team']
        }
    ];

    const inquiryTypes = [
        { value: 'sales', label: 'Sales Inquiry' },
        { value: 'support', label: 'Technical Support' },
        { value: 'partnership', label: 'Partnership' },
        { value: 'demo', label: 'Request Demo' },
        { value: 'billing', label: 'Billing Question' },
        { value: 'other', label: 'Other' }
    ];

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!formData.name || !formData.email || !formData.message) {
            toast.error('Please fill in all required fields');
            return;
        }

        try {
            setIsSubmitting(true);

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));

            toast.success('Message sent successfully! We\'ll get back to you soon.');

            // Reset form
            setFormData({
                name: '',
                email: '',
                company: '',
                subject: '',
                message: '',
                inquiry_type: ''
            });

        } catch (error) {
            toast.error('Something went wrong. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <section id="contact" className="py-20 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                        Get in Touch
                    </h2>
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                        Have questions about Accord? We're here to help. Reach out to our team
                        and we'll get back to you as soon as possible.
                    </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
                    {/* Contact Methods */}
                    {contactMethods.map((method, index) => (
                        <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                            <CardHeader>
                                <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                                    <method.icon className="h-6 w-6 text-blue-600" />
                                </div>
                                <CardTitle className="text-xl">{method.title}</CardTitle>
                                <CardDescription>{method.description}</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="font-semibold text-gray-900 mb-4">{method.contact}</p>
                                <Button variant="outline" className="w-full">
                                    {method.action}
                                </Button>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Send className="h-5 w-5 mr-2" />
                                Send us a Message
                            </CardTitle>
                            <CardDescription>
                                Fill out the form below and we'll get back to you within 24 hours.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="name">
                                            Full Name <span className="text-red-500">*</span>
                                        </Label>
                                        <Input
                                            id="name"
                                            value={formData.name}
                                            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                                            placeholder="Your full name"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="email">
                                            Email Address <span className="text-red-500">*</span>
                                        </Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={formData.email}
                                            onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                                            placeholder="<EMAIL>"
                                            required
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="company">Company</Label>
                                        <Input
                                            id="company"
                                            value={formData.company}
                                            onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
                                            placeholder="Your company name"
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="inquiry_type">Inquiry Type</Label>
                                        <Select
                                            value={formData.inquiry_type}
                                            onValueChange={(value) => setFormData(prev => ({ ...prev, inquiry_type: value }))}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select inquiry type" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {inquiryTypes.map((type) => (
                                                    <SelectItem key={type.value} value={type.value}>
                                                        {type.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="subject">Subject</Label>
                                    <Input
                                        id="subject"
                                        value={formData.subject}
                                        onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
                                        placeholder="Brief subject of your message"
                                    />
                                </div>

                                <div>
                                    <Label htmlFor="message">
                                        Message <span className="text-red-500">*</span>
                                    </Label>
                                    <Textarea
                                        id="message"
                                        value={formData.message}
                                        onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                                        placeholder="Tell us how we can help you..."
                                        rows={5}
                                        required
                                    />
                                </div>

                                <Button
                                    type="submit"
                                    className="w-full bg-blue-600 hover:bg-blue-700"
                                    disabled={isSubmitting}
                                >
                                    {isSubmitting ? (
                                        'Sending Message...'
                                    ) : (
                                        <>
                                            Send Message
                                            <Send className="ml-2 h-4 w-4" />
                                        </>
                                    )}
                                </Button>
                            </form>
                        </CardContent>
                    </Card>

                    <div className="space-y-8">
                        <div>
                            <h3 className="text-2xl font-bold text-gray-900 mb-6">
                                Visit Our Office
                            </h3>
                            <div className="space-y-6">
                                {officeInfo.map((info, index) => (
                                    <div key={index} className="flex items-start">
                                        <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                            <info.icon className="h-5 w-5 text-blue-600" />
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-gray-900 mb-2">{info.title}</h4>
                                            <div className="space-y-1">
                                                {info.details.map((detail, idx) => (
                                                    <p key={idx} className="text-gray-600">{detail}</p>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <Card className="bg-blue-50 border-blue-200">
                            <CardHeader>
                                <CardTitle className="flex items-center text-blue-900">
                                    <Headphones className="h-5 w-5 mr-2" />
                                    Need Immediate Help?
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p className="text-blue-800 mb-4">
                                    Our support team is available 24/7 to help you with any urgent issues.
                                </p>
                                <Button className="w-full bg-blue-600 hover:bg-blue-700">
                                    Contact Support Now
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </div> */}
            </div>
        </section>
    );
};

export default ContactSection;
