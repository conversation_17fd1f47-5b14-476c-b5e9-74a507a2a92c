// import React from 'react';
// import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router';
// import { Toaster } from 'react-hot-toast';
// import { useAuth } from './contexts/AuthContext';
// import { AuthProvider } from './contexts/AuthContext';
// import Login from './pages/Login';
// import Register from './pages/Register';
// import ForgotPassword from './pages/ForgotPassword';
// import ResetPassword from './pages/ResetPassword';

// const Dashboard = () => {
//     const { logout, user } = useAuth();

//     const handleLogout = async () => {
//         await logout();
//     };

//     return (
//         <div className="min-h-screen bg-gray-50 flex items-center justify-center">
//             <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
//                 <h1 className="text-2xl font-bold text-gray-900 mb-4">
//                     Welcome to {import.meta.env.VITE_APP_NAME || 'SaaS CRM'}
//                 </h1>
//                 <p className="text-gray-600 mb-4">
//                     Hello, {user?.name}! You have successfully logged in to your dashboard.
//                 </p>
//                 <p className="text-sm text-gray-500 mb-6">
//                     This is a placeholder dashboard. Replace this component with your actual dashboard content.
//                 </p>
//                 <button
//                     onClick={handleLogout}
//                     className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
//                 >
//                     Logout
//                 </button>
//             </div>
//         </div>
//     );
// };

// const AppRoutes = () => {
//     const { isAuthenticated, isLoading } = useAuth();

//     if (isLoading) {
//         return (
//             <div className="min-h-screen bg-gray-50 flex items-center justify-center">
//                 <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
//             </div>
//         );
//     }

//     return (
//         <Routes>
//             <Route path="/login" element={!isAuthenticated ? <Login /> : <Navigate to="/dashboard" replace />} />
//             <Route path="/register" element={!isAuthenticated ? <Register /> : <Navigate to="/dashboard" replace />} />
//             <Route path="/forgot-password" element={<ForgotPassword />} />
//             <Route path="/reset-password" element={!isAuthenticated ? <ResetPassword /> : <Navigate to="/dashboard" replace />} />
//             <Route path="/dashboard" element={isAuthenticated ? <Dashboard /> : <Navigate to="/login" replace />} />
//             <Route path="/" element={<Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />} />
//             <Route path="*" element={<Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />} />
//         </Routes>
//     );
// };

// function App() {
//     const basename = import.meta.env.VITE_APP_BASENAME || '';

//     return (
//         <Router basename={basename}>
//             <AuthProvider>
//                 <div className="App">
//                     <AppRoutes />
//                     <Toaster
//                         position="top-right"
//                         toastOptions={{
//                             duration: 4000,
//                             style: {
//                                 background: '#363636',
//                                 color: '#fff',
//                             },
//                             success: {
//                                 duration: 3000,
//                                 theme: {
//                                     primary: 'green',
//                                     secondary: 'black',
//                                 },
//                             },
//                             error: {
//                                 duration: 5000,
//                                 theme: {
//                                     primary: 'red',
//                                     secondary: 'black',
//                                 },
//                             },
//                         }}
//                     />
//                 </div>
//             </AuthProvider>
//         </Router>
//     );
// }

// export default App;


import { AdminLayout } from 'admin-layout';
import 'admin-layout/styles/admin-layout.css';
import { BrowserRouter as Router } from 'react-router-dom';

function App() {
    const user = {
        name: 'John Doe',
        email: '<EMAIL>',
        avatar: null, // Will show initials
        role: 'Administrator'
    };

    const notifications = [
        {
            id: '1',
            title: 'New user registered',
            message: 'A new user has joined your platform',
            type: 'info',
            timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
            read: false
        },
        {
            id: '2',
            title: 'Payment received',
            message: 'Payment of $99.99 processed successfully',
            type: 'success',
            timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
            read: false
        },
        {
            id: '3',
            title: 'Server maintenance',
            message: 'Scheduled maintenance at 2:00 AM',
            type: 'warning',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
            read: true
        }
    ];

    return (
        <Router>
            <AdminLayout
                user={user}
                notifications={notifications}
                onSidebarToggle={(collapsed) => console.log('Sidebar toggled:', collapsed)}
            >
                <div className="space-y-6">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
                        <p className="text-muted-foreground">Welcome to your admin dashboard</p>
                    </div>

                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div className="bg-card p-6 rounded-lg border">
                            <h3 className="text-sm font-medium text-muted-foreground">Total Users</h3>
                            <p className="text-2xl font-bold text-foreground">1,234</p>
                            <p className="text-xs text-green-600">+12% from last month</p>
                        </div>

                        <div className="bg-card p-6 rounded-lg border">
                            <h3 className="text-sm font-medium text-muted-foreground">Revenue</h3>
                            <p className="text-2xl font-bold text-foreground">$12,345</p>
                            <p className="text-xs text-green-600">+8% from last month</p>
                        </div>

                        <div className="bg-card p-6 rounded-lg border">
                            <h3 className="text-sm font-medium text-muted-foreground">Orders</h3>
                            <p className="text-2xl font-bold text-foreground">567</p>
                            <p className="text-xs text-red-600">-3% from last month</p>
                        </div>

                        <div className="bg-card p-6 rounded-lg border">
                            <h3 className="text-sm font-medium text-muted-foreground">Active Sessions</h3>
                            <p className="text-2xl font-bold text-foreground">89</p>
                            <p className="text-xs text-green-600">+15% from last hour</p>
                        </div>
                    </div>

                    {/* Content Section */}
                    <div className="bg-card p-6 rounded-lg border">
                        <h2 className="text-xl font-semibold text-foreground mb-4">Recent Activity</h2>
                        <div className="space-y-3">
                            <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <span className="text-sm text-foreground">New user registration</span>
                                <span className="text-xs text-muted-foreground ml-auto">2 min ago</span>
                            </div>
                            <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span className="text-sm text-foreground">Payment processed</span>
                                <span className="text-xs text-muted-foreground ml-auto">5 min ago</span>
                            </div>
                            <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                <span className="text-sm text-foreground">System update completed</span>
                                <span className="text-xs text-muted-foreground ml-auto">1 hour ago</span>
                            </div>
                        </div>
                    </div>
                </div>
            </AdminLayout>
        </Router>
    );
}

export default App;
