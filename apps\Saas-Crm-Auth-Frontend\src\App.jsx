// import React from 'react';
// import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router';
// import { Toaster } from 'react-hot-toast';
// import { useAuth } from './contexts/AuthContext';
// import { AuthProvider } from './contexts/AuthContext';
// import Login from './pages/Login';
// import Register from './pages/Register';
// import ForgotPassword from './pages/ForgotPassword';
// import ResetPassword from './pages/ResetPassword';

// const Dashboard = () => {
//     const { logout, user } = useAuth();

//     const handleLogout = async () => {
//         await logout();
//     };

//     return (
//         <div className="min-h-screen bg-gray-50 flex items-center justify-center">
//             <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
//                 <h1 className="text-2xl font-bold text-gray-900 mb-4">
//                     Welcome to {import.meta.env.VITE_APP_NAME || 'SaaS CRM'}
//                 </h1>
//                 <p className="text-gray-600 mb-4">
//                     Hello, {user?.name}! You have successfully logged in to your dashboard.
//                 </p>
//                 <p className="text-sm text-gray-500 mb-6">
//                     This is a placeholder dashboard. Replace this component with your actual dashboard content.
//                 </p>
//                 <button
//                     onClick={handleLogout}
//                     className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
//                 >
//                     Logout
//                 </button>
//             </div>
//         </div>
//     );
// };

// const AppRoutes = () => {
//     const { isAuthenticated, isLoading } = useAuth();

//     if (isLoading) {
//         return (
//             <div className="min-h-screen bg-gray-50 flex items-center justify-center">
//                 <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
//             </div>
//         );
//     }

//     return (
//         <Routes>
//             <Route path="/login" element={!isAuthenticated ? <Login /> : <Navigate to="/dashboard" replace />} />
//             <Route path="/register" element={!isAuthenticated ? <Register /> : <Navigate to="/dashboard" replace />} />
//             <Route path="/forgot-password" element={<ForgotPassword />} />
//             <Route path="/reset-password" element={!isAuthenticated ? <ResetPassword /> : <Navigate to="/dashboard" replace />} />
//             <Route path="/dashboard" element={isAuthenticated ? <Dashboard /> : <Navigate to="/login" replace />} />
//             <Route path="/" element={<Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />} />
//             <Route path="*" element={<Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />} />
//         </Routes>
//     );
// };

// function App() {
//     const basename = import.meta.env.VITE_APP_BASENAME || '';

//     return (
//         <Router basename={basename}>
//             <AuthProvider>
//                 <div className="App">
//                     <AppRoutes />
//                     <Toaster
//                         position="top-right"
//                         toastOptions={{
//                             duration: 4000,
//                             style: {
//                                 background: '#363636',
//                                 color: '#fff',
//                             },
//                             success: {
//                                 duration: 3000,
//                                 theme: {
//                                     primary: 'green',
//                                     secondary: 'black',
//                                 },
//                             },
//                             error: {
//                                 duration: 5000,
//                                 theme: {
//                                     primary: 'red',
//                                     secondary: 'black',
//                                 },
//                             },
//                         }}
//                     />
//                 </div>
//             </AuthProvider>
//         </Router>
//     );
// }

// export default App;


import { AdminLayout } from 'admin-layout';
import 'admin-layout/styles/admin-layout.css';

function App() {
    return (
        <AdminLayout
            user={{ name: 'Admin', email: '<EMAIL>' }}
            notifications={[]}
        >
            <h1>Dashboard Content</h1>
        </AdminLayout>
    );
}
