import React from 'react';
import { Menu, Search } from 'lucide-react';
import { cn } from '../lib/utils.js';
import { Button } from './ui/button.jsx';
import { Input } from './ui/input.jsx';
import Breadcrumb from './Breadcrumb.jsx';
import UserMenu from './UserMenu.jsx';
import ThemeToggle from './ThemeToggle.jsx';
import NotificationBell from './NotificationBell.jsx';

const Header = ({ 
  user,
  notifications = [],
  onSidebarToggle,
  sidebarCollapsed = false,
  showSearch = true,
  showBreadcrumb = true,
  className,
  ...props 
}) => {
  return (
    <header 
      className={cn(
        "flex h-16 items-center gap-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-6",
        className
      )}
      {...props}
    >
      {/* Mobile Sidebar Toggle */}
      <Button
        variant="ghost"
        size="icon"
        onClick={onSidebarToggle}
        className="md:hidden"
      >
        <Menu className="h-4 w-4" />
      </Button>

      {/* Breadcrumb */}
      {showBreadcrumb && (
        <div className="hidden md:flex">
          <Breadcrumb />
        </div>
      )}

      {/* Spacer */}
      <div className="flex-1" />

      {/* Search */}
      {showSearch && (
        <div className="relative hidden md:flex w-full max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search..."
            className="pl-10 pr-4"
          />
        </div>
      )}

      {/* Right Side Actions */}
      <div className="flex items-center gap-2">
        {/* Theme Toggle */}
        <ThemeToggle />
        
        {/* Notifications */}
        <NotificationBell notifications={notifications} />
        
        {/* User Menu */}
        <UserMenu user={user} />
      </div>
    </header>
  );
};

export default Header;
