import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  ChevronLeft, 
  ChevronRight, 
  Home, 
  Users, 
  Settings, 
  BarChart3,
  FileText,
  Mail,
  Calendar,
  Package,
  CreditCard,
  Shield,
  HelpCircle
} from 'lucide-react';
import { cn } from '../lib/utils.js';
import { Button } from './ui/button.jsx';
import { ScrollArea } from './ui/scroll-area.jsx';
import { Separator } from './ui/separator.jsx';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip.jsx';

const defaultNavigation = [
  {
    title: 'Dashboard',
    href: '/admin',
    icon: Home,
    badge: null
  },
  {
    title: 'Users',
    href: '/admin/users',
    icon: Users,
    badge: '12'
  },
  {
    title: 'Analytics',
    href: '/admin/analytics',
    icon: BarChart3,
    badge: null
  },
  {
    title: 'Reports',
    href: '/admin/reports',
    icon: FileText,
    badge: null
  },
  {
    title: 'Messages',
    href: '/admin/messages',
    icon: Mail,
    badge: '3'
  },
  {
    title: 'Calendar',
    href: '/admin/calendar',
    icon: Calendar,
    badge: null
  },
  {
    title: 'Products',
    href: '/admin/products',
    icon: Package,
    badge: null
  },
  {
    title: 'Billing',
    href: '/admin/billing',
    icon: CreditCard,
    badge: null
  },
  {
    title: 'Security',
    href: '/admin/security',
    icon: Shield,
    badge: null
  },
  {
    title: 'Settings',
    href: '/admin/settings',
    icon: Settings,
    badge: null
  },
  {
    title: 'Help',
    href: '/admin/help',
    icon: HelpCircle,
    badge: null
  }
];

const Sidebar = ({ 
  collapsed = false, 
  onToggle, 
  navigation = defaultNavigation,
  className,
  ...props 
}) => {
  const location = useLocation();

  const NavItem = ({ item, collapsed }) => {
    const isActive = location.pathname === item.href;
    
    const content = (
      <Link
        to={item.href}
        className={cn(
          "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-all hover:bg-accent hover:text-accent-foreground",
          isActive 
            ? "bg-primary text-primary-foreground hover:bg-primary/90" 
            : "text-muted-foreground",
          collapsed && "justify-center px-2"
        )}
      >
        <item.icon className={cn("h-4 w-4 shrink-0")} />
        {!collapsed && (
          <>
            <span className="truncate">{item.title}</span>
            {item.badge && (
              <span className={cn(
                "ml-auto rounded-full px-2 py-0.5 text-xs font-medium",
                isActive 
                  ? "bg-primary-foreground/20 text-primary-foreground" 
                  : "bg-muted text-muted-foreground"
              )}>
                {item.badge}
              </span>
            )}
          </>
        )}
      </Link>
    );

    if (collapsed) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              {content}
            </TooltipTrigger>
            <TooltipContent side="right" className="flex items-center gap-2">
              {item.title}
              {item.badge && (
                <span className="rounded-full bg-primary px-2 py-0.5 text-xs text-primary-foreground">
                  {item.badge}
                </span>
              )}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    return content;
  };

  return (
    <div 
      className={cn(
        "flex h-screen flex-col border-r bg-card transition-all duration-300 ease-in-out",
        collapsed ? "w-16" : "w-64",
        className
      )}
      {...props}
    >
      {/* Header */}
      <div className="flex h-16 items-center border-b px-4">
        {!collapsed && (
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">A</span>
            </div>
            <span className="font-semibold text-foreground">Admin Panel</span>
          </div>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggle}
          className={cn(
            "h-8 w-8 shrink-0",
            collapsed ? "mx-auto" : "ml-auto"
          )}
        >
          {collapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <nav className="space-y-1">
          {navigation.map((item, index) => (
            <NavItem key={index} item={item} collapsed={collapsed} />
          ))}
        </nav>
      </ScrollArea>

      {/* Footer */}
      <div className="border-t p-4">
        {!collapsed && (
          <div className="text-xs text-muted-foreground text-center">
            Admin Panel v1.0
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
